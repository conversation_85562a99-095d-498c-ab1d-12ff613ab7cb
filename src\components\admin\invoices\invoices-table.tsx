'use client'

import React from 'react'
import {
  ArrowUpIcon,
  ArrowDownIcon,
  PencilIcon,
  TrashIcon,
  CheckIcon,
  EyeIcon
} from '@heroicons/react/24/outline'

interface Invoice {
  id: string
  clientId: number
  totalAmount: number
  subtotal?: number
  taxRate: number
  taxAmount: number
  status: string
  dueDate: string
  description?: string
  paidAt?: string
  createdAt: string
  updatedAt: string
  clients?: {
    id: number
    companyname: string
    contactname: string
    contactemail: string
  }
  [key: string]: any
}

interface InvoicesTableProps {
  invoices: Invoice[]
  visibleColumns: string[]
  displayDensity: string
  sortBy: string
  sortOrder: string
  selectedInvoices: string[]
  actionLoading: string | null
  enableBulkActions: boolean
  onSort: (field: string) => void
  onSelectAll: () => void
  onSelectInvoice: (invoiceId: string) => void
  onAction: (action: string, invoice: Invoice) => void
  formatDate: (dateString: string) => string
  formatCurrency: (amount: number) => string
}

export function InvoicesTable({
  invoices,
  visibleColumns,
  displayDensity,
  sortBy,
  sortOrder,
  selectedInvoices,
  actionLoading,
  enableBulkActions,
  onSort,
  onSelectAll,
  onSelectInvoice,
  onAction,
  formatDate,
  formatCurrency
}: InvoicesTableProps) {
  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'sent':
        return 'bg-blue-100 text-blue-800'
      case 'paid':
        return 'bg-green-100 text-green-800'
      case 'overdue':
        return 'bg-red-100 text-red-800'
      case 'cancelled':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getNestedValue = (obj: any, path: string) => {
    return path.split('.').reduce((current, key) => current?.[key], obj)
  }

  return (
    <div className="bg-white shadow-sm rounded-lg overflow-hidden">
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              {/* Checkbox column for bulk actions */}
              {enableBulkActions && (
                <th className={`px-3 text-left ${displayDensity === 'compact' ? 'py-2' : 'py-3'} w-12`}>
                  <input
                    type="checkbox"
                    checked={selectedInvoices.length === invoices.length && invoices.length > 0}
                    onChange={onSelectAll}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                </th>
              )}

              {/* Dynamic columns based on visibility settings */}
              {visibleColumns.includes('id') && (
                <th
                  className={`px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 w-32 ${
                    displayDensity === 'compact' ? 'py-2' : 'py-3'
                  }`}
                  onClick={() => onSort('id')}
                >
                  <div className="flex items-center space-x-1">
                    <span>Invoice #</span>
                    {sortBy === 'id' && (
                      sortOrder === 'asc' ? <ArrowUpIcon className="h-3 w-3" /> : <ArrowDownIcon className="h-3 w-3" />
                    )}
                  </div>
                </th>
              )}

              {visibleColumns.includes('client.companyName') && (
                <th
                  className={`px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 w-48 ${
                    displayDensity === 'compact' ? 'py-2' : 'py-3'
                  }`}
                  onClick={() => onSort('clients.companyname')}
                >
                  <div className="flex items-center space-x-1">
                    <span>Client</span>
                    {sortBy === 'clients.companyname' && (
                      sortOrder === 'asc' ? <ArrowUpIcon className="h-3 w-3" /> : <ArrowDownIcon className="h-3 w-3" />
                    )}
                  </div>
                </th>
              )}

              {visibleColumns.includes('totalAmount') && (
                <th
                  className={`px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 w-36 ${
                    displayDensity === 'compact' ? 'py-2' : 'py-3'
                  }`}
                  onClick={() => onSort('totalAmount')}
                >
                  <div className="flex items-center space-x-1">
                    <span>Total Amount</span>
                    {sortBy === 'totalAmount' && (
                      sortOrder === 'asc' ? <ArrowUpIcon className="h-3 w-3" /> : <ArrowDownIcon className="h-3 w-3" />
                    )}
                  </div>
                </th>
              )}

              {visibleColumns.includes('status') && (
                <th
                  className={`px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 w-32 ${
                    displayDensity === 'compact' ? 'py-2' : 'py-3'
                  }`}
                  onClick={() => onSort('status')}
                >
                  <div className="flex items-center space-x-1">
                    <span>Status</span>
                    {sortBy === 'status' && (
                      sortOrder === 'asc' ? <ArrowUpIcon className="h-3 w-3" /> : <ArrowDownIcon className="h-3 w-3" />
                    )}
                  </div>
                </th>
              )}

              {visibleColumns.includes('dueDate') && (
                <th
                  className={`px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 w-32 ${
                    displayDensity === 'compact' ? 'py-2' : 'py-3'
                  }`}
                  onClick={() => onSort('dueDate')}
                >
                  <div className="flex items-center space-x-1">
                    <span>Due Date</span>
                    {sortBy === 'dueDate' && (
                      sortOrder === 'asc' ? <ArrowUpIcon className="h-3 w-3" /> : <ArrowDownIcon className="h-3 w-3" />
                    )}
                  </div>
                </th>
              )}

              {visibleColumns.includes('paidAt') && (
                <th
                  className={`px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 w-32 ${
                    displayDensity === 'compact' ? 'py-2' : 'py-3'
                  }`}
                  onClick={() => onSort('paidAt')}
                >
                  <div className="flex items-center space-x-1">
                    <span>Paid Date</span>
                    {sortBy === 'paidAt' && (
                      sortOrder === 'asc' ? <ArrowUpIcon className="h-3 w-3" /> : <ArrowDownIcon className="h-3 w-3" />
                    )}
                  </div>
                </th>
              )}

              {visibleColumns.includes('subtotal') && (
                <th
                  className={`px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 w-32 ${
                    displayDensity === 'compact' ? 'py-2' : 'py-3'
                  }`}
                  onClick={() => onSort('subtotal')}
                >
                  <div className="flex items-center space-x-1">
                    <span>Subtotal</span>
                    {sortBy === 'subtotal' && (
                      sortOrder === 'asc' ? <ArrowUpIcon className="h-3 w-3" /> : <ArrowDownIcon className="h-3 w-3" />
                    )}
                  </div>
                </th>
              )}

              {visibleColumns.includes('taxAmount') && (
                <th
                  className={`px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 w-32 ${
                    displayDensity === 'compact' ? 'py-2' : 'py-3'
                  }`}
                  onClick={() => onSort('taxAmount')}
                >
                  <div className="flex items-center space-x-1">
                    <span>Tax Amount</span>
                    {sortBy === 'taxAmount' && (
                      sortOrder === 'asc' ? <ArrowUpIcon className="h-3 w-3" /> : <ArrowDownIcon className="h-3 w-3" />
                    )}
                  </div>
                </th>
              )}

              {visibleColumns.includes('taxRate') && (
                <th
                  className={`px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 w-28 ${
                    displayDensity === 'compact' ? 'py-2' : 'py-3'
                  }`}
                  onClick={() => onSort('taxRate')}
                >
                  <div className="flex items-center space-x-1">
                    <span>Tax Rate</span>
                    {sortBy === 'taxRate' && (
                      sortOrder === 'asc' ? <ArrowUpIcon className="h-3 w-3" /> : <ArrowDownIcon className="h-3 w-3" />
                    )}
                  </div>
                </th>
              )}

              {visibleColumns.includes('createdAt') && (
                <th
                  className={`px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 w-32 ${
                    displayDensity === 'compact' ? 'py-2' : 'py-3'
                  }`}
                  onClick={() => onSort('createdAt')}
                >
                  <div className="flex items-center space-x-1">
                    <span>Created</span>
                    {sortBy === 'createdAt' && (
                      sortOrder === 'asc' ? <ArrowUpIcon className="h-3 w-3" /> : <ArrowDownIcon className="h-3 w-3" />
                    )}
                  </div>
                </th>
              )}

              {visibleColumns.includes('updatedAt') && (
                <th
                  className={`px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 w-32 ${
                    displayDensity === 'compact' ? 'py-2' : 'py-3'
                  }`}
                  onClick={() => onSort('updatedAt')}
                >
                  <div className="flex items-center space-x-1">
                    <span>Last Updated</span>
                    {sortBy === 'updatedAt' && (
                      sortOrder === 'asc' ? <ArrowUpIcon className="h-3 w-3" /> : <ArrowDownIcon className="h-3 w-3" />
                    )}
                  </div>
                </th>
              )}

              <th className={`px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-32 ${
                displayDensity === 'compact' ? 'py-2' : 'py-3'
              }`}>
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {invoices.map((invoice) => (
              <tr key={invoice.id} className={`hover:bg-gray-50 ${selectedInvoices.includes(String(invoice.id)) ? 'bg-blue-50 border-l-4 border-blue-500' : ''}`}>
                {/* Checkbox cell for bulk actions */}
                {enableBulkActions && (
                  <td className={`px-3 ${displayDensity === 'compact' ? 'py-2' : 'py-4'} w-12`}>
                    <input
                      type="checkbox"
                      checked={selectedInvoices.includes(String(invoice.id))}
                      onChange={() => onSelectInvoice(String(invoice.id))}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                  </td>
                )}

                {/* Dynamic cells based on visibility settings */}
                {visibleColumns.includes('id') && (
                  <td className={`px-4 ${displayDensity === 'compact' ? 'py-2' : 'py-3'}`}>
                    <div className="font-medium text-gray-900">#{invoice.id}</div>
                  </td>
                )}

                {visibleColumns.includes('client.companyName') && (
                  <td className={`px-4 ${displayDensity === 'compact' ? 'py-2' : 'py-3'}`}>
                    <div className="text-sm text-gray-900 truncate">
                      {getNestedValue(invoice, 'clients.companyname') || 'N/A'}
                    </div>
                  </td>
                )}

                {visibleColumns.includes('totalAmount') && (
                  <td className={`px-4 ${displayDensity === 'compact' ? 'py-2' : 'py-3'} text-sm text-gray-900 font-medium`}>
                    {formatCurrency(Number(invoice.totalamount || invoice.totalAmount || 0))}
                  </td>
                )}

                {visibleColumns.includes('status') && (
                  <td className={`px-4 ${displayDensity === 'compact' ? 'py-2' : 'py-3'}`}>
                    <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(invoice.status)}`}>
                      {invoice.status}
                    </span>
                  </td>
                )}

                {visibleColumns.includes('dueDate') && (
                  <td className={`px-4 ${displayDensity === 'compact' ? 'py-2' : 'py-3'} text-sm text-gray-900`}>
                    {formatDate(invoice.duedate || invoice.dueDate)}
                  </td>
                )}

                {visibleColumns.includes('paidAt') && (
                  <td className={`px-4 ${displayDensity === 'compact' ? 'py-2' : 'py-3'} text-sm text-gray-900`}>
                    {(invoice.paidat || invoice.paidAt) ? formatDate(invoice.paidat || invoice.paidAt) : '-'}
                  </td>
                )}

                {visibleColumns.includes('subtotal') && (
                  <td className={`px-4 ${displayDensity === 'compact' ? 'py-2' : 'py-3'} text-sm text-gray-900`}>
                    {(invoice.subtotal) ? formatCurrency(Number(invoice.subtotal)) : '-'}
                  </td>
                )}

                {visibleColumns.includes('taxAmount') && (
                  <td className={`px-4 ${displayDensity === 'compact' ? 'py-2' : 'py-3'} text-sm text-gray-900`}>
                    {formatCurrency(Number(invoice.taxamount || invoice.taxAmount || 0))}
                  </td>
                )}

                {visibleColumns.includes('taxRate') && (
                  <td className={`px-4 ${displayDensity === 'compact' ? 'py-2' : 'py-3'} text-sm text-gray-900`}>
                    {Number(invoice.taxrate || invoice.taxRate || 0)}%
                  </td>
                )}

                {visibleColumns.includes('createdAt') && (
                  <td className={`px-4 ${displayDensity === 'compact' ? 'py-2' : 'py-3'} text-sm text-gray-900`}>
                    {formatDate(invoice.createdat || invoice.createdAt)}
                  </td>
                )}

                {visibleColumns.includes('updatedAt') && (
                  <td className={`px-4 ${displayDensity === 'compact' ? 'py-2' : 'py-3'} text-sm text-gray-900`}>
                    {formatDate(invoice.updatedat || invoice.updatedAt)}
                  </td>
                )}

                <td className={`px-4 ${displayDensity === 'compact' ? 'py-2' : 'py-3'} text-sm font-medium`}>
                  <div className="flex items-center justify-center space-x-1">
                    <button
                      onClick={() => onAction('view', invoice)}
                      disabled={actionLoading === `view-${invoice.id}`}
                      className={`${displayDensity === 'compact' ? 'p-1.5' : 'p-2'} rounded-md transition-all duration-200 border border-transparent hover:border-solid text-gray-600 hover:text-gray-800 hover:bg-gray-50 hover:border-gray-200 ${actionLoading === `view-${invoice.id}` ? 'opacity-50 cursor-not-allowed' : ''}`}
                      title="View invoice details"
                    >
                      {actionLoading === `view-${invoice.id}` ? (
                        <div className={`border-2 border-current border-t-transparent rounded-full animate-spin ${
                          displayDensity === 'compact' ? 'w-3 h-3' : 'w-4 h-4'
                        }`} />
                      ) : (
                        <EyeIcon className={displayDensity === 'compact' ? 'w-3 h-3' : 'w-4 h-4'} />
                      )}
                    </button>

                    <button
                      onClick={() => onAction('edit', invoice)}
                      disabled={actionLoading === `edit-${invoice.id}`}
                      className={`${displayDensity === 'compact' ? 'p-1.5' : 'p-2'} rounded-md transition-all duration-200 border border-transparent hover:border-solid text-blue-600 hover:text-blue-800 hover:bg-blue-50 hover:border-blue-200 ${actionLoading === `edit-${invoice.id}` ? 'opacity-50 cursor-not-allowed' : ''}`}
                      title="Edit invoice"
                    >
                      {actionLoading === `edit-${invoice.id}` ? (
                        <div className={`border-2 border-current border-t-transparent rounded-full animate-spin ${
                          displayDensity === 'compact' ? 'w-3 h-3' : 'w-4 h-4'
                        }`} />
                      ) : (
                        <PencilIcon className={displayDensity === 'compact' ? 'w-3 h-3' : 'w-4 h-4'} />
                      )}
                    </button>

                    <button
                      onClick={() => onAction('toggle-status', invoice)}
                      disabled={actionLoading === `toggle-status-${invoice.id}`}
                      className={`${displayDensity === 'compact' ? 'p-1.5' : 'p-2'} rounded-md transition-all duration-200 border border-transparent hover:border-solid text-green-600 hover:text-green-800 hover:bg-green-50 hover:border-green-200 ${actionLoading === `toggle-status-${invoice.id}` ? 'opacity-50 cursor-not-allowed' : ''}`}
                      title="Mark invoice as paid"
                    >
                      {actionLoading === `toggle-status-${invoice.id}` ? (
                        <div className={`border-2 border-current border-t-transparent rounded-full animate-spin ${
                          displayDensity === 'compact' ? 'w-3 h-3' : 'w-4 h-4'
                        }`} />
                      ) : (
                        <CheckIcon className={displayDensity === 'compact' ? 'w-3 h-3' : 'w-4 h-4'} />
                      )}
                    </button>

                    <button
                      onClick={() => onAction('delete', invoice)}
                      disabled={actionLoading === `delete-${invoice.id}`}
                      className={`${displayDensity === 'compact' ? 'p-1.5' : 'p-2'} rounded-md transition-all duration-200 border border-transparent hover:border-solid text-red-600 hover:text-red-800 hover:bg-red-50 hover:border-red-200 ${actionLoading === `delete-${invoice.id}` ? 'opacity-50 cursor-not-allowed' : ''}`}
                      title="Delete invoice"
                    >
                      {actionLoading === `delete-${invoice.id}` ? (
                        <div className={`border-2 border-current border-t-transparent rounded-full animate-spin ${
                          displayDensity === 'compact' ? 'w-3 h-3' : 'w-4 h-4'
                        }`} />
                      ) : (
                        <TrashIcon className={displayDensity === 'compact' ? 'w-3 h-3' : 'w-4 h-4'} />
                      )}
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  )
}
