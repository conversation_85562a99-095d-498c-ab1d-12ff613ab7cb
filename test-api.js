// Simple Node.js script to test the API
const fetch = require('node-fetch');

async function testAPI() {
  console.log('Testing Invoice API...\n');
  
  try {
    // Test 1: Basic API endpoint
    console.log('1. Testing basic API endpoint...');
    const response = await fetch('http://localhost:3000/api/admin/invoices');
    console.log('Status:', response.status);
    console.log('Status Text:', response.statusText);
    console.log('Headers:', Object.fromEntries(response.headers.entries()));
    
    const text = await response.text();
    console.log('Response Body:', text);
    
    if (response.ok) {
      try {
        const data = JSON.parse(text);
        console.log('Parsed JSON:', JSON.stringify(data, null, 2));
      } catch (e) {
        console.log('Failed to parse as JSON:', e.message);
      }
    }
    
  } catch (error) {
    console.error('Network Error:', error.message);
    console.error('Full Error:', error);
  }
  
  console.log('\n' + '='.repeat(50) + '\n');
  
  try {
    // Test 2: Test endpoint
    console.log('2. Testing database test endpoint...');
    const testResponse = await fetch('http://localhost:3000/api/test/invoices');
    console.log('Status:', testResponse.status);
    
    const testText = await testResponse.text();
    console.log('Response Body:', testText);
    
    if (testResponse.ok) {
      try {
        const testData = JSON.parse(testText);
        console.log('Parsed JSON:', JSON.stringify(testData, null, 2));
      } catch (e) {
        console.log('Failed to parse as JSON:', e.message);
      }
    }
    
  } catch (error) {
    console.error('Network Error:', error.message);
  }
  
  console.log('\n' + '='.repeat(50) + '\n');
  
  try {
    // Test 3: Mock endpoint
    console.log('3. Testing mock endpoint...');
    const mockResponse = await fetch('http://localhost:3000/api/admin/invoices-mock');
    console.log('Status:', mockResponse.status);
    
    const mockText = await mockResponse.text();
    console.log('Response Body:', mockText);
    
    if (mockResponse.ok) {
      try {
        const mockData = JSON.parse(mockText);
        console.log('Parsed JSON:', JSON.stringify(mockData, null, 2));
      } catch (e) {
        console.log('Failed to parse as JSON:', e.message);
      }
    }
    
  } catch (error) {
    console.error('Network Error:', error.message);
  }
}

testAPI();
