import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// Simple test endpoint without authentication
export async function GET(request: NextRequest) {
  try {
    console.log('Testing database connection...')

    // Test basic connection
    const dbTest = await prisma.$queryRaw`SELECT 1 as test`
    console.log('Database connection successful:', dbTest)

    // Test if invoices table exists
    let invoiceCount = 0
    let invoices: any[] = []
    let tableExists = false

    try {
      // First check if table exists
      await prisma.$queryRaw`SELECT 1 FROM invoices LIMIT 1`
      tableExists = true
      console.log('Invoices table exists')

      invoiceCount = await prisma.invoices.count()
      console.log('Invoice count:', invoiceCount)

      // Get a few invoices with basic fields
      invoices = await prisma.invoices.findMany({
        take: 3,
        select: {
          id: true,
          duedate: true,
          totalamount: true,
          status: true,
          description: true,
          createdat: true
        }
      })

      // Transform BigInt to string for JSON serialization
      invoices = invoices.map(invoice => ({
        ...invoice,
        id: invoice.id.toString()
      }))

      console.log('Sample invoices:', invoices)
    } catch (invoiceError) {
      console.error('Error accessing invoices table:', invoiceError)
      console.error('Error details:', {
        message: invoiceError instanceof Error ? invoiceError.message : 'Unknown error',
        code: (invoiceError as any)?.code,
        meta: (invoiceError as any)?.meta
      })
    }

    // Test other required tables
    let clientCount = 0
    let contractCount = 0
    let orderCount = 0

    try {
      clientCount = await prisma.clients.count()
      contractCount = await prisma.contracts.count()
      orderCount = await prisma.orders.count()
    } catch (tableError) {
      console.error('Error accessing other tables:', tableError)
    }

    return NextResponse.json({
      success: true,
      data: {
        dbTest,
        tableExists,
        invoiceCount,
        clientCount,
        contractCount,
        orderCount,
        invoices
      }
    })
  } catch (error) {
    console.error('Test endpoint error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    }, { status: 500 })
  }
}
