import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { 
  with<PERSON>rror<PERSON>and<PERSON>, 
  successResponse,
  paginatedResponse,
  requireAdmin,
  getQueryParams,
  getPaginationParams,
  buildSearchQuery,
  buildSortQuery,
  validateRequest
} from '@/lib/api-utils'
import { schemas } from '@/lib/validations'
import { transformToDbFields, transformFromDbFields } from '@/lib/data-transform'

// GET /api/admin/invoices - Get all invoices with pagination and search
export const GET = withErrorHandler(async (request: NextRequest) => {
  try {
    console.log('GET /api/admin/invoices - Starting request')

    // TODO: Re-enable admin check after testing
    // await requireAdmin(request)

    const { page, limit, search, sortBy, sortOrder, filter } = getQueryParams(request)
    const { skip, take } = getPaginationParams(page, limit)

    console.log('Query params:', { page, limit, search, sortBy, sortOrder, filter })

    // Build search query
    const searchQuery = buildSearchQuery(search, ['description'])

    // Add status filter if provided
    if (filter) {
      searchQuery.status = filter
    }

    console.log('Search query:', searchQuery)

    // Build sort query
    const sortQuery = buildSortQuery(sortBy, sortOrder)
    console.log('Sort query:', sortQuery)

    // Test basic database connection first
    console.log('Testing database connection...')
    const dbTest = await prisma.$queryRaw`SELECT 1 as test`
    console.log('Database connection successful:', dbTest)

    // Get invoices count first
    console.log('Getting invoices count...')
    const total = await prisma.invoices.count({ where: searchQuery })
    console.log('Total invoices:', total)

    // Get invoices with minimal data first
    console.log('Getting invoices...')
    const invoices = await prisma.invoices.findMany({
      where: searchQuery,
      select: {
        id: true,
        duedate: true,
        subtotal: true,
        taxrate: true,
        taxamount: true,
        totalamount: true,
        status: true,
        description: true,
        paidat: true,
        createdat: true,
        updatedat: true,
        clientid: true,
        contid: true,
        orderid: true,
        projectid: true
      },
      orderBy: sortQuery,
      skip,
      take,
    })

    console.log('Found invoices:', invoices.length)

    return paginatedResponse(invoices, page, limit, total)
  } catch (error) {
    console.error('Error in GET /api/admin/invoices:', error)
    throw error
  }
})

// POST /api/admin/invoices - Create a new invoice
export const POST = withErrorHandler(async (request: NextRequest) => {
  // TODO: Re-enable admin check after testing
  // await requireAdmin(request)

  const body = await request.json()
  console.log('Received invoice data:', JSON.stringify(body, null, 2))

  // Transform form data to database field names
  const invoiceData = {
    clientid: Number(body.clientId),
    projectid: body.projectId ? Number(body.projectId) : undefined,
    orderid: body.orderId ? Number(body.orderId) : undefined,
    contid: body.contractId ? Number(body.contractId) : undefined,
    description: body.description || undefined,
    subtotal: Number(body.subtotal) || undefined,
    taxrate: Number(body.taxRate) || 0,
    taxamount: Number(body.taxAmount) || 0,
    totalamount: Number(body.totalAmount) || 0,
    status: body.status || 'Pending',
    duedate: new Date(body.dueDate),
    paidat: body.paidAt ? new Date(body.paidAt) : undefined,
  }

  // Validate invoice data
  console.log('Validating invoice data:', JSON.stringify(invoiceData, null, 2))
  const validatedData = schemas.invoice.create.parse(invoiceData)

  // Create invoice
  const invoice = await prisma.invoices.create({
    data: validatedData,
    include: {
      clients: {
        select: {
          id: true,
          companyname: true,
          contactname: true,
          contactemail: true
        }
      },
      projects: {
        select: {
          id: true,
          name: true,
          status: true
        }
      },
      contracts: {
        select: {
          id: true,
          contname: true,
          contstatus: true
        }
      },
      orders: {
        select: {
          id: true,
          ordertitle: true,
          status: true
        }
      },
      invoiceitems: true,
      payments: {
        select: {
          id: true,
          amount: true,
          status: true,
          paidat: true
        }
      }
    }
  })

  return successResponse(invoice, 'Invoice created successfully', 201)
})
