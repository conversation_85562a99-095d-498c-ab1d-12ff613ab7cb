import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { 
  with<PERSON>rror<PERSON><PERSON><PERSON>, 
  successResponse,
  paginatedResponse,
  requireAdmin,
  getQueryParams,
  getPaginationParams,
  buildSearchQuery,
  buildSortQuery,
  validateRequest
} from '@/lib/api-utils'
import { schemas } from '@/lib/validations'
import { transformToDbFields, transformFromDbFields } from '@/lib/data-transform'

// GET /api/admin/invoices - Get all invoices with pagination and search
export const GET = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)

  const { page, limit, search, sortBy, sortOrder, filter } = getQueryParams(request)
  const { skip, take } = getPaginationParams(page, limit)

  // Build search query
  const searchQuery = buildSearchQuery(search, ['invoiceNumber', 'description'])
  
  // Add status filter if provided
  if (filter) {
    searchQuery.status = filter
  }
  
  // Build sort query
  const sortQuery = buildSortQuery(sortBy, sortOrder)

  // Get invoices with pagination
  const [invoices, total] = await Promise.all([
    prisma.invoice.findMany({
      where: searchQuery,
      include: {
        clients: {
          select: {
            id: true,
            companyname: true,
            contactname: true,
            contactemail: true
          }
        },
        projects: {
          select: {
            id: true,
            name: true,
            status: true
          }
        },
        orders: {
          select: {
            id: true,
            ordertitle: true,
            status: true
          }
        },
        contracts: {
          select: {
            id: true,
            contname: true,
            contstatus: true
          }
        },
        invoiceitems: true,
        payments: {
          select: {
            id: true,
            amount: true,
            status: true,
            paidat: true
          }
        }
      },
      orderBy: sortQuery,
      skip,
      take,
    }),
    prisma.invoice.count({ where: searchQuery })
  ])

  return paginatedResponse(invoices, page, limit, total)
})

// POST /api/admin/invoices - Create a new invoice
export const POST = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)

  const body = await request.json()
  console.log('Received invoice data:', JSON.stringify(body, null, 2))

  // Transform form data to database field names
  const invoiceData = {
    clientid: Number(body.clientId),
    projectid: body.projectId ? Number(body.projectId) : undefined,
    orderid: body.orderId ? Number(body.orderId) : undefined,
    contid: body.contractId ? Number(body.contractId) : undefined,
    description: body.description || undefined,
    subtotal: Number(body.subtotal) || undefined,
    taxrate: Number(body.taxRate) || 0,
    taxamount: Number(body.taxAmount) || 0,
    totalamount: Number(body.totalAmount) || 0,
    status: body.status || 'Pending',
    duedate: new Date(body.dueDate),
    paidat: body.paidAt ? new Date(body.paidAt) : undefined,
  }

  // Validate invoice data
  console.log('Validating invoice data:', JSON.stringify(invoiceData, null, 2))
  const validatedData = schemas.invoice.create.parse(invoiceData)

  // Create invoice
  const invoice = await prisma.invoice.create({
    data: validatedData,
    include: {
      where: { id: newInvoice.id },
      include: {
        clients: {
          select: {
            id: true,
            companyname: true,
            contactname: true,
            contactemail: true
          }
        },
        projects: {
          select: {
            id: true,
            name: true,
            status: true
          }
        },
        contracts: {
          select: {
            id: true,
            contname: true,
            contstatus: true
          }
        },
        orders: {
          select: {
            id: true,
            ordertitle: true,
            status: true
          }
        },
        invoiceitems: true,
        payments: {
          select: {
            id: true,
            amount: true,
            status: true,
            paidat: true
          }
        }
      }
    })
  })

  return successResponse(invoice, 'Invoice created successfully', 201)
})
