'use client'

import React, { useState, useEffect } from 'react'
import { XMarkIcon } from '@heroicons/react/24/outline'
import { CrudConfig } from '../crud/types'

interface Invoice {
  id?: string
  clientId?: number
  totalAmount?: number
  subtotal?: number
  taxRate?: number
  taxAmount?: number
  status?: string
  dueDate?: string
  description?: string
  paidAt?: string
  [key: string]: any
}

interface InvoiceModalProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (data: any) => Promise<void>
  config: CrudConfig<Invoice>
  title: string
  initialData?: Invoice
}

export function InvoiceModal({
  isOpen,
  onClose,
  onSubmit,
  config,
  title,
  initialData
}: InvoiceModalProps) {
  const [formData, setFormData] = useState<any>({})
  const [loading, setLoading] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [clients, setClients] = useState<any[]>([])
  const [contracts, setContracts] = useState<any[]>([])
  const [orders, setOrders] = useState<any[]>([])

  useEffect(() => {
    if (isOpen) {
      // Initialize form data
      const initialFormData: any = {}

      config.fields.forEach(field => {
        if (initialData && initialData[field.key] !== undefined) {
          initialFormData[field.key] = initialData[field.key]
        } else if (field.defaultValue !== undefined) {
          initialFormData[field.key] = field.defaultValue
        } else {
          initialFormData[field.key] = ''
        }
      })

      setFormData(initialFormData)
      setErrors({})

      // Fetch dropdown data
      fetchDropdownData()
    }
  }, [isOpen, initialData, config.fields])

  const fetchDropdownData = async () => {
    try {
      const [clientsRes, contractsRes, ordersRes] = await Promise.all([
        fetch('/api/admin/clients'),
        fetch('/api/admin/contracts'),
        fetch('/api/admin/orders')
      ])

      if (clientsRes.ok) {
        const clientsData = await clientsRes.json()
        setClients(clientsData.data || [])
      }

      if (contractsRes.ok) {
        const contractsData = await contractsRes.json()
        setContracts(contractsData.data || [])
      }

      if (ordersRes.ok) {
        const ordersData = await ordersRes.json()
        setOrders(ordersData.data || [])
      }
    } catch (error) {
      console.error('Error fetching dropdown data:', error)
    }
  }

  const handleInputChange = (key: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [key]: value
    }))

    // Clear error when user starts typing
    if (errors[key]) {
      setErrors(prev => ({
        ...prev,
        [key]: ''
      }))
    }

    // Auto-calculate total amount when subtotal or tax changes
    if (key === 'subtotal' || key === 'taxRate') {
      const subtotal = parseFloat(key === 'subtotal' ? value : formData.subtotal) || 0
      const taxRate = parseFloat(key === 'taxRate' ? value : formData.taxRate) || 0
      const taxAmount = (subtotal * taxRate) / 100
      const totalAmount = subtotal + taxAmount

      setFormData(prev => ({
        ...prev,
        taxAmount: taxAmount.toFixed(2),
        totalAmount: totalAmount.toFixed(2)
      }))
    }
  }

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    config.fields.forEach(field => {
      if (field.required && (!formData[field.key] || formData[field.key] === '')) {
        newErrors[field.key] = `${field.label} is required`
      }

      if (field.type === 'email' && formData[field.key]) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
        if (!emailRegex.test(formData[field.key])) {
          newErrors[field.key] = 'Please enter a valid email address'
        }
      }

      if (field.type === 'number' && formData[field.key]) {
        const numValue = parseFloat(formData[field.key])
        if (isNaN(numValue)) {
          newErrors[field.key] = 'Please enter a valid number'
        }
      }
    })

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    setLoading(true)

    try {
      // Convert string numbers to actual numbers for numeric fields
      const processedData = { ...formData }
      config.fields.forEach(field => {
        if (field.type === 'number' && processedData[field.key]) {
          processedData[field.key] = parseFloat(processedData[field.key])
        }
      })

      await onSubmit(processedData)
      onClose()
    } catch (error) {
      console.error('Error submitting form:', error)
    } finally {
      setLoading(false)
    }
  }

  const renderField = (field: any) => {
    const value = formData[field.key] || ''
    const error = errors[field.key]

    switch (field.type) {
      case 'select':
        let options = field.options || []

        // Special handling for dropdowns
        if (field.key === 'clientId') {
          options = [
            { value: '', label: 'Select Client' },
            ...clients.map(client => ({
              value: client.id,
              label: client.companyname || client.companyName
            }))
          ]
        } else if (field.key === 'contractId') {
          options = [
            { value: '', label: 'Select Contract' },
            ...contracts.map(contract => ({
              value: contract.id,
              label: contract.contname || `Contract ${contract.id}`
            }))
          ]
        } else if (field.key === 'orderId') {
          options = [
            { value: '', label: 'Select Order' },
            ...orders.map(order => ({
              value: order.id,
              label: order.ordertitle || `Order ${order.id}`
            }))
          ]
        }

        return (
          <div key={field.key} className="space-y-1">
            <label className="block text-sm font-medium text-gray-700">
              {field.label}
              {field.required && <span className="text-red-500 ml-1">*</span>}
            </label>
            <select
              value={value}
              onChange={(e) => handleInputChange(field.key, e.target.value)}
              className={`block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                error ? 'border-red-300' : 'border-gray-300'
              }`}
            >
              {options.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
            {error && <p className="text-sm text-red-600">{error}</p>}
          </div>
        )

      case 'textarea':
        return (
          <div key={field.key} className="space-y-1">
            <label className="block text-sm font-medium text-gray-700">
              {field.label}
              {field.required && <span className="text-red-500 ml-1">*</span>}
            </label>
            <textarea
              value={value}
              onChange={(e) => handleInputChange(field.key, e.target.value)}
              rows={field.rows || 3}
              placeholder={field.placeholder}
              className={`block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                error ? 'border-red-300' : 'border-gray-300'
              }`}
            />
            {error && <p className="text-sm text-red-600">{error}</p>}
          </div>
        )

      case 'boolean':
        return (
          <div key={field.key} className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={value}
              onChange={(e) => handleInputChange(field.key, e.target.checked)}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label className="text-sm font-medium text-gray-700">
              {field.label}
            </label>
          </div>
        )

      default:
        return (
          <div key={field.key} className="space-y-1">
            <label className="block text-sm font-medium text-gray-700">
              {field.label}
              {field.required && <span className="text-red-500 ml-1">*</span>}
            </label>
            <input
              type={field.type}
              value={value}
              onChange={(e) => handleInputChange(field.key, e.target.value)}
              placeholder={field.placeholder}
              step={field.type === 'number' ? '0.01' : undefined}
              className={`block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                error ? 'border-red-300' : 'border-gray-300'
              }`}
            />
            {error && <p className="text-sm text-red-600">{error}</p>}
          </div>
        )
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={onClose} />

        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
          <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">{title}</h3>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600"
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </div>

            <form onSubmit={handleSubmit} className="space-y-6">
              {config.formLayout?.sections ? (
                config.formLayout.sections.map((section, sectionIndex) => (
                  <div key={sectionIndex} className="space-y-4">
                    <h4 className="text-md font-medium text-gray-900 border-b border-gray-200 pb-2">
                      {section.title}
                    </h4>
                    <div className={`grid grid-cols-1 gap-4 ${
                      config.formLayout?.columns === 3 ? 'md:grid-cols-3' :
                      config.formLayout?.columns === 2 ? 'md:grid-cols-2' : ''
                    }`}>
                      {section.fields.map(fieldKey => {
                        const field = config.fields.find(f => f.key === fieldKey)
                        return field ? renderField(field) : null
                      })}
                    </div>
                  </div>
                ))
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {config.fields.map(field => renderField(field))}
                </div>
              )}

              <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
                <button
                  type="button"
                  onClick={onClose}
                  className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={loading}
                  className="px-4 py-2 bg-blue-600 border border-transparent rounded-md text-sm font-medium text-white hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {loading ? 'Saving...' : (initialData ? 'Update' : 'Create')}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  )
}
