import { NextRequest, NextResponse } from 'next/server'

// Mock API endpoint for testing frontend
export async function GET(request: NextRequest) {
  try {
    console.log('Mock invoices API called')
    
    // Mock invoice data
    const mockInvoices = [
      {
        id: '1',
        clientid: '1',
        contid: '1',
        orderid: '1',
        projectid: '1',
        description: 'Test Invoice 1',
        subtotal: 1000,
        taxrate: 10,
        taxamount: 100,
        totalamount: 1100,
        status: 'Pending',
        duedate: '2024-01-15',
        paidat: null,
        createdat: '2024-01-01',
        updatedat: '2024-01-01'
      },
      {
        id: '2',
        clientid: '2',
        contid: '2',
        orderid: '2',
        projectid: null,
        description: 'Test Invoice 2',
        subtotal: 2000,
        taxrate: 10,
        taxamount: 200,
        totalamount: 2200,
        status: 'Paid',
        duedate: '2024-01-20',
        paidat: '2024-01-18',
        createdat: '2024-01-05',
        updatedat: '2024-01-18'
      }
    ]
    
    return NextResponse.json({
      success: true,
      data: mockInvoices,
      total: 2,
      page: 1,
      limit: 10,
      totalPages: 1
    })
  } catch (error) {
    console.error('Mock API error:', error)
    return NextResponse.json({
      success: false,
      error: 'Mock API error'
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    console.log('Mock POST request:', body)
    
    // Return mock created invoice
    const mockInvoice = {
      id: '3',
      ...body,
      createdat: new Date().toISOString(),
      updatedat: new Date().toISOString()
    }
    
    return NextResponse.json({
      success: true,
      data: mockInvoice,
      message: 'Invoice created successfully'
    }, { status: 201 })
  } catch (error) {
    console.error('Mock POST error:', error)
    return NextResponse.json({
      success: false,
      error: 'Mock POST error'
    }, { status: 500 })
  }
}
