'use client'

import React, { useState, useEffect, useRef } from 'react'
import {
  MagnifyingGlassIcon,
  XMarkIcon,
  FunnelIcon,
  Squares2X2Icon,
  ListBulletIcon,
  RectangleStackIcon,
  AdjustmentsHorizontalIcon,
  EyeIcon,
  EyeSlashIcon,
  ChevronDownIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  PlusIcon,
  PencilIcon,
  TrashIcon,
  CheckIcon,
  DocumentTextIcon,
  CurrencyDollarIcon
} from '@heroicons/react/24/outline'
import { InvoiceModal } from './invoice-modal'
import { InvoicesTable } from './invoices-table'
import { CrudConfig } from '../crud/types'
import { motion, AnimatePresence } from 'framer-motion'

interface Invoice {
  id: string
  clientid: number
  totalamount: number
  subtotal?: number
  taxrate: number
  taxamount: number
  status: string
  duedate: string
  description?: string
  paidat?: string
  createdat: string
  updatedat: string
  clients?: {
    id: number
    companyname: string
    contactname: string
    contactemail: string
  }
  [key: string]: any
}

interface InvoicesManagerProps {
  config: CrudConfig<Invoice>
}

export function InvoicesManager({ config }: InvoicesManagerProps) {
  const [invoices, setInvoices] = useState<Invoice[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedInvoices, setSelectedInvoices] = useState<string[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [editingInvoice, setEditingInvoice] = useState<Invoice | null>(null)
  const [actionLoading, setActionLoading] = useState<string | null>(null)
  const searchInputRef = useRef<HTMLInputElement>(null)

  // View controls state
  const [viewMode, setViewMode] = useState('list')
  const [displayDensity, setDisplayDensity] = useState('comfortable')
  const [visibleColumns, setVisibleColumns] = useState([
    'id', 'client.companyName', 'totalAmount', 'status', 'dueDate', 'createdAt'
  ])
  const [sortBy, setSortBy] = useState('createdAt')
  const [sortOrder, setSortOrder] = useState('desc')
  const [showFilters, setShowFilters] = useState(false)
  const [showColumnMenu, setShowColumnMenu] = useState(false)
  const [showDensityMenu, setShowDensityMenu] = useState(false)
  const [showSortMenu, setShowSortMenu] = useState(false)

  // Debounce search query
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery)
    }, 300)

    return () => clearTimeout(timer)
  }, [searchQuery])

  // Fetch invoices
  const fetchInvoices = async (preserveFocus = false) => {
    try {
      // Only show full loading for initial load, not for search
      if (!preserveFocus) {
        setLoading(true)
      }

      const params = new URLSearchParams({
        page: currentPage.toString(),
        sortBy: sortBy,
        sortOrder: sortOrder,
        ...(debouncedSearchQuery && { search: debouncedSearchQuery }),
      })

      console.log('Fetching invoices with params:', params.toString()) // Debug log

      const response = await fetch(`/api/admin/${config.endpoint}?${params}`)
      if (!response.ok) throw new Error('Failed to fetch invoices')

      const data = await response.json()
      console.log('Received invoices data:', data) // Debug log

      setInvoices(data.data || [])
      setTotalPages(Math.ceil((data.total || 0) / (config.pageSize || 10)))
      setError(null) // Clear any previous errors on successful fetch
    } catch (err) {
      console.error('Error fetching invoices:', err) // Debug log
      setError(err instanceof Error ? err.message : 'Failed to fetch invoices')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    // Preserve focus when searching
    const isSearching = debouncedSearchQuery !== ''
    fetchInvoices(isSearching)
  }, [currentPage, debouncedSearchQuery, sortBy, sortOrder])

  // Handle create
  const handleCreate = async (formData: any) => {
    try {
      const response = await fetch(`/api/admin/${config.endpoint}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData),
      })
      
      if (!response.ok) throw new Error('Failed to create invoice')

      setIsCreateModalOpen(false)
      fetchInvoices()
      alert('Invoice created successfully!')
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create invoice'
      setError(errorMessage)
      alert(errorMessage)
      throw err
    }
  }

  // Handle update
  const handleUpdate = async (id: string, formData: any) => {
    try {
      console.log('Updating invoice with data:', formData)

      const response = await fetch(`/api/admin/${config.endpoint}/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData),
      })

      console.log('Update response status:', response.status)

      if (!response.ok) {
        const errorData = await response.json()
        console.error('Update error response:', errorData)
        throw new Error(errorData.error || `Failed to update invoice (${response.status})`)
      }

      const result = await response.json()
      console.log('Update success:', result)

      setIsEditModalOpen(false)
      setEditingInvoice(null)
      fetchInvoices()
      alert('Invoice updated successfully!')
    } catch (err) {
      console.error('Update error:', err)
      const errorMessage = err instanceof Error ? err.message : 'Failed to update invoice'
      setError(errorMessage)
      alert(errorMessage)
      throw err
    }
  }

  // Handle delete
  const handleDelete = async (id: string) => {
    try {
      const response = await fetch(`/api/admin/${config.endpoint}/${id}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to delete invoice')
      }

      // Show success message
      setError(null)
      fetchInvoices()

      // Optional: Show success notification
      console.log('Invoice deleted successfully')
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete invoice'
      setError(errorMessage)
      console.error('Delete error:', err)
    }
  }

  // Handle individual actions
  const handleAction = async (action: string, item: Invoice) => {
    const actionKey = `${action}-${item.id}`

    try {
      setActionLoading(actionKey)

      switch (action) {
        case 'view':
          // Open invoice details in new tab or modal
          window.open(`/admin/invoices/${item.id}`, '_blank')
          break

        case 'edit':
          setEditingInvoice(item)
          setIsEditModalOpen(true)
          break

        case 'toggle-status':
          await handleToggleStatus(item.id, item.status === 'Paid' ? 'Pending' : 'Paid')
          break

        case 'delete':
          const deleteAction = config.actions?.find(a => a.action === 'delete')
          const confirmMessage = deleteAction?.confirmationMessage || 'Are you sure you want to delete this invoice? This action cannot be undone.'

          if (window.confirm(confirmMessage)) {
            await handleDelete(item.id)
          }
          break

        default:
          console.warn(`Unknown action: ${action}`)
      }
    } finally {
      setActionLoading(null)
    }
  }

  // Toggle invoice status
  const handleToggleStatus = async (id: string, status: string) => {
    try {
      const response = await fetch(`/api/admin/${config.endpoint}/${id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status }),
      })

      if (!response.ok) throw new Error('Failed to update invoice status')

      fetchInvoices()
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update invoice status')
    }
  }

  const formatDate = (dateString: string) => {
    if (!dateString) return '-'
    return new Date(dateString).toLocaleDateString()
  }

  const formatCurrency = (amount: number) => {
    if (!amount) return '$0.00'
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount)
  }

  // Handle bulk actions
  const handleBulkAction = async (action: string, invoiceIds: string[]) => {
    try {
      let endpoint = `/api/admin/${config.endpoint}`
      let method = 'PUT'
      let body: any = { ids: invoiceIds }

      switch (action) {
        case 'mark-sent':
          body.data = { status: 'Sent' }
          break
        case 'mark-paid':
          body.data = { status: 'Paid' }
          break
        case 'mark-overdue':
          body.data = { status: 'Overdue' }
          break
        case 'delete':
          method = 'DELETE'
          body = { ids: invoiceIds }
          break
        default:
          throw new Error(`Unknown bulk action: ${action}`)
      }

      const response = await fetch(endpoint, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || `Failed to ${action} invoices`)
      }

      const result = await response.json()

      if (result.success) {
        setSelectedInvoices([])
        fetchInvoices()
        // Show success notification
      } else {
        throw new Error(result.error || `Failed to ${action} invoices`)
      }
    } catch (error) {
      console.error(`Error performing bulk ${action}:`, error)
      throw error
    }
  }

  // Handle select all
  const handleSelectAll = () => {
    if (selectedInvoices.length === invoices.length) {
      setSelectedInvoices([])
    } else {
      setSelectedInvoices(invoices.map(invoice => String(invoice.id)))
    }
  }

  // Handle select individual invoice
  const handleSelectInvoice = (invoiceId: string) => {
    setSelectedInvoices(prev =>
      prev.includes(invoiceId)
        ? prev.filter(id => id !== invoiceId)
        : [...prev, invoiceId]
    )
  }

  // View control functions
  const handleSort = (field) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')
    } else {
      setSortBy(field)
      setSortOrder('asc')
    }
  }

  const handleColumnToggle = (columnKey) => {
    const isVisible = visibleColumns.includes(columnKey)
    if (isVisible) {
      setVisibleColumns(prev => prev.filter(col => col !== columnKey))
    } else {
      setVisibleColumns(prev => [...prev, columnKey])
    }
  }

  const resetViewSettings = () => {
    setVisibleColumns(['id', 'client.companyName', 'totalAmount', 'status', 'dueDate', 'createdAt'])
    setViewMode('list')
    setDisplayDensity('comfortable')
    setSortBy('createdAt')
    setSortOrder('desc')
  }

  const availableColumns = [
    { key: 'id', label: 'Invoice #', hideable: false },
    { key: 'client.companyName', label: 'Client', hideable: true },
    { key: 'totalAmount', label: 'Total Amount', hideable: true },
    { key: 'status', label: 'Status', hideable: true },
    { key: 'dueDate', label: 'Due Date', hideable: true },
    { key: 'paidAt', label: 'Paid Date', hideable: true },
    { key: 'subtotal', label: 'Subtotal', hideable: true },
    { key: 'taxAmount', label: 'Tax Amount', hideable: true },
    { key: 'taxRate', label: 'Tax Rate', hideable: true },
    { key: 'createdAt', label: 'Created', hideable: true },
    { key: 'updatedAt', label: 'Last Updated', hideable: true },
  ]

  const viewModeIcons = {
    list: ListBulletIcon,
    grid: Squares2X2Icon,
    card: RectangleStackIcon,
  }

  const densityLabels = {
    compact: 'Compact',
    comfortable: 'Comfortable',
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6 mx-4 sm:mx-6 lg:mx-8 xl:mx-12 2xl:mx-16">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">{config.title}</h1>
          {config.description && (
            <p className="text-gray-600">{config.description}</p>
          )}
        </div>
        <button
          onClick={() => setIsCreateModalOpen(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2"
        >
          <span>+</span>
          <span>Add Invoice</span>
        </button>
      </div>

      {/* Comprehensive Search and Controls Section */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 space-y-4">
        {/* Search and Primary Controls */}
        <div className="flex items-center justify-between space-x-4">
          <div className="flex items-center space-x-4 flex-1">
            {/* Search Bar */}
            <div className="flex-1 relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
              </div>
              <input
                ref={searchInputRef}
                type="text"
                placeholder={config.searchPlaceholder || 'Search invoices by ID, client, description...'}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="block w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
              />
              {/* Search Loading Indicator */}
              {searchQuery !== debouncedSearchQuery && (
                <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                  <div className="animate-spin rounded-full h-4 w-4 border-2 border-blue-600 border-t-transparent"></div>
                </div>
              )}
              {/* Clear Search Button */}
              {searchQuery && (
                <button
                  onClick={() => {
                    setSearchQuery('')
                    searchInputRef.current?.focus()
                  }}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                >
                  <XMarkIcon className="h-4 w-4" />
                </button>
              )}
            </div>

            {/* Filters Button */}
            <button
              onClick={() => setShowFilters(!showFilters)}
              className={`px-4 py-3 border rounded-lg transition-colors flex items-center space-x-2 ${
                showFilters
                  ? 'bg-blue-50 border-blue-300 text-blue-700'
                  : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
              }`}
            >
              <FunnelIcon className="h-4 w-4" />
              <span>Filters</span>
            </button>
          </div>

          {/* View Controls */}
          <div className="flex items-center space-x-2 bg-gray-50 border border-gray-200 rounded-lg p-1">
            {/* View Mode Controls */}
            <div className="flex items-center bg-white rounded-md p-1">
              {Object.entries(viewModeIcons).map(([mode, Icon]) => (
                <button
                  key={mode}
                  onClick={() => setViewMode(mode as 'list' | 'grid' | 'card')}
                  className={`p-2 rounded transition-colors ${
                    viewMode === mode
                      ? 'bg-blue-600 text-white shadow-sm'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                  }`}
                  title={`${mode.charAt(0).toUpperCase() + mode.slice(1)} view`}
                >
                  <Icon className="h-4 w-4" />
                </button>
              ))}
            </div>

            {/* Display Density Controls */}
            <div className="relative">
              <button
                onClick={() => setShowDensityMenu(!showDensityMenu)}
                className="flex items-center space-x-2 px-3 py-2 text-sm text-gray-700 hover:text-gray-900 hover:bg-white rounded-md transition-colors"
                title="Display density"
              >
                <AdjustmentsHorizontalIcon className="h-4 w-4" />
                <span>{densityLabels[displayDensity]}</span>
                <ChevronDownIcon className="h-3 w-3" />
              </button>

              <AnimatePresence>
                {showDensityMenu && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="absolute top-full left-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 min-w-[140px]"
                  >
                    {Object.entries(densityLabels).map(([density, label]) => (
                      <button
                        key={density}
                        onClick={() => {
                          setDisplayDensity(density as 'compact' | 'comfortable')
                          setShowDensityMenu(false)
                        }}
                        className={`w-full text-left px-3 py-2 text-sm hover:bg-gray-50 first:rounded-t-lg last:rounded-b-lg transition-colors ${
                          displayDensity === density
                            ? 'bg-blue-50 text-blue-700'
                            : 'text-gray-700'
                        }`}
                      >
                        {label}
                      </button>
                    ))}
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            {/* Column Visibility Controls */}
            <div className="relative">
              <button
                onClick={() => setShowColumnMenu(!showColumnMenu)}
                className="flex items-center space-x-2 px-3 py-2 text-sm text-gray-700 hover:text-gray-900 hover:bg-white rounded-md transition-colors"
                title="Column visibility"
              >
                <EyeIcon className="h-4 w-4" />
                <span>Columns ({visibleColumns.length}/{availableColumns.filter(col => col.hideable !== false).length})</span>
                <ChevronDownIcon className="h-3 w-3" />
              </button>

              <AnimatePresence>
                {showColumnMenu && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="absolute top-full right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 min-w-[200px] max-h-64 overflow-y-auto"
                  >
                    <div className="p-2">
                      <div className="flex items-center justify-between mb-2 pb-2 border-b border-gray-100">
                        <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                          Column Visibility
                        </span>
                        <button
                          onClick={() => {
                            resetViewSettings()
                            setShowColumnMenu(false)
                          }}
                          className="text-xs text-blue-600 hover:text-blue-800"
                        >
                          Reset
                        </button>
                      </div>

                      {availableColumns
                        .filter(col => col.hideable !== false)
                        .map((column) => {
                          const isVisible = visibleColumns.includes(column.key)
                          return (
                            <label
                              key={column.key}
                              className="flex items-center space-x-2 py-1 hover:bg-gray-50 rounded cursor-pointer"
                            >
                              <input
                                type="checkbox"
                                checked={isVisible}
                                onChange={() => handleColumnToggle(column.key)}
                                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                              />
                              <span className="text-sm text-gray-700">{column.label}</span>
                              {isVisible ? (
                                <EyeIcon className="h-3 w-3 text-gray-400" />
                              ) : (
                                <EyeSlashIcon className="h-3 w-3 text-gray-400" />
                              )}
                            </label>
                          )
                        })}
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            {/* Sort Controls */}
            <div className="relative">
              <button
                onClick={() => setShowSortMenu(!showSortMenu)}
                className="flex items-center space-x-2 px-3 py-2 text-sm text-gray-700 hover:text-gray-900 hover:bg-white rounded-md transition-colors"
                title="Sort options"
              >
                {sortOrder === 'asc' ? (
                  <ArrowUpIcon className="h-4 w-4" />
                ) : (
                  <ArrowDownIcon className="h-4 w-4" />
                )}
                <span>Sort</span>
                <ChevronDownIcon className="h-3 w-3" />
              </button>

              <AnimatePresence>
                {showSortMenu && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="absolute top-full right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 min-w-[180px]"
                  >
                    <div className="p-2">
                      <div className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-2 pb-2 border-b border-gray-100">
                        Sort By
                      </div>
                      {availableColumns.map((column) => (
                        <button
                          key={column.key}
                          onClick={() => {
                            handleSort(column.key)
                            setShowSortMenu(false)
                          }}
                          className={`w-full text-left px-3 py-2 text-sm hover:bg-gray-50 rounded transition-colors flex items-center justify-between ${
                            sortBy === column.key
                              ? 'bg-blue-50 text-blue-700'
                              : 'text-gray-700'
                          }`}
                        >
                          <span>{column.label}</span>
                          {sortBy === column.key && (
                            sortOrder === 'asc' ? (
                              <ArrowUpIcon className="h-3 w-3" />
                            ) : (
                              <ArrowDownIcon className="h-3 w-3" />
                            )
                          )}
                        </button>
                      ))}
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </div>
        </div>

        {/* Active Filters Display */}
        {(searchQuery || showFilters) && (
          <div className="flex items-center space-x-2 pt-2 border-t border-gray-100">
            <span className="text-sm text-gray-500">Active:</span>
            {searchQuery && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                Search: "{searchQuery}"
                <button
                  onClick={() => setSearchQuery('')}
                  className="ml-1 text-blue-600 hover:text-blue-800"
                >
                  <XMarkIcon className="h-3 w-3" />
                </button>
              </span>
            )}
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
              Sort: {availableColumns.find(col => col.key === sortBy)?.label} ({sortOrder === 'asc' ? 'A-Z' : 'Z-A'})
            </span>
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
              View: {viewMode.charAt(0).toUpperCase() + viewMode.slice(1)}
            </span>
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
              Density: {densityLabels[displayDensity]}
            </span>
          </div>
        )}
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
          {error}
        </div>
      )}

      {/* Bulk Actions */}
      {config.enableBulkActions && selectedInvoices.length > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <span className="text-sm font-medium text-blue-900">
                {selectedInvoices.length} invoice{selectedInvoices.length !== 1 ? 's' : ''} selected
              </span>
            </div>
            <div className="flex items-center space-x-2">
              {config.bulkActions?.map((action) => (
                <button
                  key={action.action}
                  onClick={() => {
                    if (action.requiresConfirmation) {
                      if (window.confirm(action.confirmationMessage || `Are you sure you want to ${action.action} the selected invoices?`)) {
                        handleBulkAction(action.action, selectedInvoices)
                      }
                    } else {
                      handleBulkAction(action.action, selectedInvoices)
                    }
                  }}
                  className={`inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md ${
                    action.variant === 'danger'
                      ? 'text-red-700 bg-red-100 hover:bg-red-200'
                      : action.variant === 'warning'
                      ? 'text-yellow-700 bg-yellow-100 hover:bg-yellow-200'
                      : action.variant === 'success'
                      ? 'text-green-700 bg-green-100 hover:bg-green-200'
                      : 'text-blue-700 bg-blue-100 hover:bg-blue-200'
                  }`}
                >
                  {action.label}
                </button>
              ))}
              <button
                onClick={() => setSelectedInvoices([])}
                className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
              >
                Clear Selection
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Invoices Display */}
      {viewMode === 'list' ? (
        /* Table View */
        <InvoicesTable
          invoices={invoices}
          visibleColumns={visibleColumns}
          displayDensity={displayDensity}
          sortBy={sortBy}
          sortOrder={sortOrder}
          selectedInvoices={selectedInvoices}
          actionLoading={actionLoading}
          enableBulkActions={config.enableBulkActions || false}
          onSort={handleSort}
          onSelectAll={handleSelectAll}
          onSelectInvoice={handleSelectInvoice}
          onAction={handleAction}
          formatDate={formatDate}
          formatCurrency={formatCurrency}
        />
      ) : (
        /* Grid/Card View - Placeholder */
        <div className="bg-white shadow-sm rounded-lg p-8 text-center">
          <DocumentTextIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">Grid/Card View</h3>
          <p className="mt-1 text-sm text-gray-500">
            Grid and card views are coming soon. Use list view for now.
          </p>
        </div>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6 rounded-lg shadow-sm">
          <div className="flex-1 flex justify-between sm:hidden">
            <button
              onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
              disabled={currentPage === 1}
              className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>
            <button
              onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
              disabled={currentPage === totalPages}
              className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
            </button>
          </div>
          <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p className="text-sm text-gray-700">
                Showing page <span className="font-medium">{currentPage}</span> of{' '}
                <span className="font-medium">{totalPages}</span>
              </p>
            </div>
            <div>
              <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                <button
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                  className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Previous
                </button>
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  const page = i + 1
                  return (
                    <button
                      key={page}
                      onClick={() => setCurrentPage(page)}
                      className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                        currentPage === page
                          ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                          : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                      }`}
                    >
                      {page}
                    </button>
                  )
                })}
                <button
                  onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages}
                  className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Next
                </button>
              </nav>
            </div>
          </div>
        </div>
      )}

      {/* Modals */}
      {isCreateModalOpen && (
        <InvoiceModal
          isOpen={isCreateModalOpen}
          onClose={() => setIsCreateModalOpen(false)}
          onSubmit={handleCreate}
          config={config}
          title="Create New Invoice"
        />
      )}

      {isEditModalOpen && editingInvoice && (
        <InvoiceModal
          isOpen={isEditModalOpen}
          onClose={() => {
            setIsEditModalOpen(false)
            setEditingInvoice(null)
          }}
          onSubmit={(data) => handleUpdate(editingInvoice.id, data)}
          config={config}
          title="Edit Invoice"
          initialData={editingInvoice}
        />
      )}
    </div>
  )
}
