import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// Simple test endpoint without authentication
export async function GET(request: NextRequest) {
  try {
    console.log('Testing database connection...')
    
    // Test basic connection
    const dbTest = await prisma.$queryRaw`SELECT 1 as test`
    console.log('Database connection successful:', dbTest)
    
    // Test invoices table
    const invoiceCount = await prisma.invoices.count()
    console.log('Invoice count:', invoiceCount)
    
    // Get a few invoices with basic fields
    const invoices = await prisma.invoices.findMany({
      take: 5,
      select: {
        id: true,
        duedate: true,
        totalamount: true,
        status: true,
        description: true,
        createdat: true
      }
    })
    
    console.log('Sample invoices:', invoices)
    
    return NextResponse.json({
      success: true,
      data: {
        dbTest,
        invoiceCount,
        invoices
      }
    })
  } catch (error) {
    console.error('Test endpoint error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
