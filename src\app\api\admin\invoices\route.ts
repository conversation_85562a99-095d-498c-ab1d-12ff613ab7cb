import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import {
  with<PERSON>rror<PERSON>andler,
  successResponse,
  paginatedResponse
} from '@/lib/api-utils'

// GET /api/admin/invoices - Get all invoices with pagination and search
export const GET = async (request: NextRequest) => {
  try {
    console.log('GET /api/admin/invoices - Starting request')

    // TODO: Re-enable admin check after testing
    // await requireAdmin(request)

    // Simple query parameters
    const url = new URL(request.url)
    const page = parseInt(url.searchParams.get('page') || '1')
    const limit = Math.min(parseInt(url.searchParams.get('limit') || '10'), 100)
    const search = url.searchParams.get('search') || ''
    const sortByParam = url.searchParams.get('sortBy') || 'createdAt'
    const sortOrder = (url.searchParams.get('sortOrder') as 'asc' | 'desc') || 'desc'

    // Map frontend field names to database field names
    const fieldMapping: Record<string, string> = {
      'createdAt': 'createdat',
      'updatedAt': 'updatedat',
      'dueDate': 'duedate',
      'paidAt': 'paidat',
      'totalAmount': 'totalamount',
      'taxAmount': 'taxamount',
      'taxRate': 'taxrate',
      'clientId': 'clientid',
      'contractId': 'contid',
      'orderId': 'orderid',
      'projectId': 'projectid',
      'client.companyName': 'clients.companyname',
      'clients.companyname': 'clients.companyname'
    }

    const sortBy = fieldMapping[sortByParam] || sortByParam
    const filter = url.searchParams.get('filter') || ''

    console.log('Query params:', { page, limit, search, sortBy, sortOrder, filter })

    // Calculate pagination
    const skip = (page - 1) * limit
    const take = limit

    // Build simple where clause
    let whereClause: any = {}

    if (search) {
      whereClause.OR = [
        { description: { contains: search, mode: 'insensitive' } },
        { status: { contains: search, mode: 'insensitive' } }
      ]
    }

    if (filter) {
      whereClause.status = filter
    }

    console.log('Where clause:', JSON.stringify(whereClause, null, 2))

    // Test basic database connection first
    console.log('Testing database connection...')
    try {
      const dbTest = await prisma.$queryRaw`SELECT 1 as test`
      console.log('Database connection successful:', dbTest)
    } catch (dbError) {
      console.error('Database connection failed:', dbError)
      console.error('DB Error details:', dbError)
      return NextResponse.json({
        success: false,
        error: `Database connection failed: ${dbError instanceof Error ? dbError.message : 'Unknown error'}`,
        code: 'DATABASE_ERROR'
      }, { status: 500 })
    }

    // Get invoices count first
    console.log('Getting invoices count...')
    let total = 0
    try {
      total = await prisma.invoices.count({ where: whereClause })
      console.log('Total invoices:', total)
    } catch (countError) {
      console.error('Error counting invoices:', countError)
      return NextResponse.json({
        success: false,
        error: `Failed to count invoices: ${countError instanceof Error ? countError.message : 'Unknown error'}`,
        code: 'COUNT_ERROR'
      }, { status: 500 })
    }

    // Get invoices with related data
    console.log('Getting invoices...')
    let invoices = []
    try {
      invoices = await prisma.invoices.findMany({
        where: whereClause,
        include: {
          clients: {
            select: {
              id: true,
              companyname: true,
              contactname: true,
              contactemail: true
            }
          },
          projects: {
            select: {
              id: true,
              name: true,
              status: true
            }
          },
          contracts: {
            select: {
              id: true,
              contname: true,
              contstatus: true
            }
          },
          orders: {
            select: {
              id: true,
              ordertitle: true,
              status: true
            }
          }
        },
        orderBy: { [sortBy]: sortOrder },
        skip,
        take,
      })

      console.log('Found invoices:', invoices.length)
    } catch (findError) {
      console.error('Error finding invoices:', findError)
      return NextResponse.json({
        success: false,
        error: `Failed to fetch invoices: ${findError instanceof Error ? findError.message : 'Unknown error'}`,
        code: 'FETCH_ERROR'
      }, { status: 500 })
    }

    // The paginatedResponse function will handle BigInt serialization automatically
    return paginatedResponse(invoices, page, limit, total)
  } catch (error) {
    console.error('Error in GET /api/admin/invoices:', error)
    console.error('Error stack:', error.stack)

    // Return a proper error response
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Internal server error',
      code: 'INTERNAL_ERROR'
    }, { status: 500 })
  }
}

// POST /api/admin/invoices - Create a new invoice
export const POST = async (request: NextRequest) => {
  try {
    console.log('POST /api/admin/invoices - Starting request')

    // TODO: Re-enable admin check after testing
    // await requireAdmin(request)

    const body = await request.json()
    console.log('Received invoice data:', JSON.stringify(body, null, 2))

    // Simple data preparation without complex validation for now
    const invoiceData = {
      clientid: BigInt(body.clientId),
      projectid: body.projectId ? BigInt(body.projectId) : null,
      orderid: BigInt(body.orderId),
      contid: BigInt(body.contractId),
      description: body.description || null,
      subtotal: body.subtotal ? parseFloat(body.subtotal) : null,
      taxrate: body.taxRate ? parseFloat(body.taxRate) : 0,
      taxamount: body.taxAmount ? parseFloat(body.taxAmount) : 0,
      totalamount: parseFloat(body.totalAmount),
      status: body.status || 'Pending',
      duedate: new Date(body.dueDate),
      paidat: body.paidAt ? new Date(body.paidAt) : null,
    }

    console.log('Prepared invoice data:', JSON.stringify(invoiceData, null, 2))

    // Create invoice with minimal data
    const invoice = await prisma.invoices.create({
      data: invoiceData,
      select: {
        id: true,
        duedate: true,
        subtotal: true,
        taxrate: true,
        taxamount: true,
        totalamount: true,
        status: true,
        description: true,
        paidat: true,
        createdat: true,
        updatedat: true,
        clientid: true,
        contid: true,
        orderid: true,
        projectid: true
      }
    })

    console.log('Created invoice:', invoice)

    // The successResponse function will handle BigInt serialization automatically
    return successResponse(invoice, 'Invoice created successfully', 201)
  } catch (error) {
    console.error('Error in POST /api/admin/invoices:', error)
    console.error('Error stack:', error.stack)

    // Return a proper error response
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Internal server error',
      code: 'INTERNAL_ERROR'
    }, { status: 500 })
  }
}
