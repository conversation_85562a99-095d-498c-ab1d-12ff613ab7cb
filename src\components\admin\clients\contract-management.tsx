'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  ChartBarIcon, 
  CalendarIcon, 
  CurrencyDollarIcon,
  ClockIcon,
  MagnifyingGlassIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline'
import Link from 'next/link'

interface Client {
  id: string | number
  companyName: string
  contactName: string
  contactEmail: string
}

interface Contract {
  id: string | number
  clientId: string | number
  contName: string
  contStatus?: string
  contValue?: number
  contValueCurr?: string
  billingType?: string
  nextBillDate?: string
  contSignedDate?: string
  contExecutedDate?: string
  contExpiryDate?: string
  createdAt: string
  updatedAt?: string
  project?: {
    id: string | number
    name: string
  }
  order?: {
    id: string | number
    orderTitle: string
  }
}

interface ContractManagementProps {
  client: Client
  selectedContract: Contract | null
  onContractSelect: (contract: Contract | null) => void
}

export function ContractManagement({ client, selectedContract, onContractSelect }: ContractManagementProps) {
  const [contracts, setContracts] = useState<Contract[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')

  useEffect(() => {
    if (client) {
      fetchContracts()
    }
  }, [client])

  const fetchContracts = async () => {
    try {
      setLoading(true)
      setError(null)

      const params = new URLSearchParams({
        page: '1',
        limit: '50',
      })

      if (searchTerm) {
        params.append('search', searchTerm)
      }

      const response = await fetch(`/api/clients/${client.id}/contracts?${params}`)

      if (!response.ok) {
        throw new Error(`Failed to fetch contracts: ${response.statusText}`)
      }

      const data = await response.json()

      if (data.success) {
        setContracts(data.data || [])
      } else {
        throw new Error(data.error || 'Failed to fetch contracts')
      }
    } catch (err) {
      console.error('Error fetching contracts:', err)
      setError(err instanceof Error ? err.message : 'An error occurred')
      setContracts([])
    } finally {
      setLoading(false)
    }
  }

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    fetchContracts()
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    })
  }

  const formatCurrency = (amount: number, currency = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount)
  }

  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'active':
        return 'bg-green-100 text-green-800'
      case 'completed':
        return 'bg-blue-100 text-blue-800'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'draft':
        return 'bg-gray-100 text-gray-800'
      case 'expired':
      case 'cancelled':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'active':
      case 'completed':
        return <CheckCircleIcon className="h-4 w-4" />
      case 'expired':
      case 'cancelled':
        return <ExclamationTriangleIcon className="h-4 w-4" />
      default:
        return <ClockIcon className="h-4 w-4" />
    }
  }

  const isExpiringSoon = (expiryDate: string) => {
    const expiry = new Date(expiryDate)
    const now = new Date()
    const thirtyDaysFromNow = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000)
    return expiry <= thirtyDaysFromNow && expiry > now
  }

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-6 bg-gray-200 rounded w-1/4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          <div className="space-y-3">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="bg-gray-100 rounded-lg p-4">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
          <h3 className="text-lg font-medium text-red-900 mb-2">Error Loading Contracts</h3>
          <p className="text-sm text-red-700 mb-4">{error}</p>
          <button
            onClick={fetchContracts}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700"
          >
            Try Again
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Contracts for {client.companyName}</h2>
          <p className="text-sm text-gray-600 mt-1">
            Manage contracts and agreements for this client
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <Link
            href={`/clients/${client.id}/contracts`}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
          >
            View All Contracts
          </Link>
        </div>
      </div>

      {/* Search Bar */}
      <div className="mb-6">
        <form onSubmit={handleSearch} className="flex items-center space-x-4">
          <div className="flex-1 relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Search contracts..."
            />
          </div>
          <button
            type="submit"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700"
          >
            Search
          </button>
        </form>
      </div>

      {/* Contracts List */}
      {contracts.length === 0 ? (
        <div className="text-center py-12">
          <ChartBarIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No contracts found</h3>
          <p className="mt-1 text-sm text-gray-500">
            {searchTerm ? 'Try adjusting your search terms.' : 'This client doesn\'t have any contracts yet.'}
          </p>
        </div>
      ) : (
        <div className="space-y-4">
          {contracts.map((contract) => (
            <motion.div
              key={contract.id}
              onClick={() => onContractSelect(contract)}
              className={`border-2 rounded-lg p-6 cursor-pointer transition-all duration-200 hover:shadow-lg ${
                selectedContract?.id === contract.id
                  ? 'border-orange-500 bg-orange-50 shadow-md'
                  : 'border-gray-200 hover:border-gray-300 bg-white'
              }`}
              whileHover={{ scale: 1.01 }}
              whileTap={{ scale: 0.99 }}
            >
              <div className="flex items-start justify-between">
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-3">
                    <h3 className="text-lg font-medium text-gray-900 truncate">
                      {contract.contName}
                    </h3>
                    
                    {/* Status Badge */}
                    {contract.contStatus && (
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(contract.contStatus)}`}>
                        {getStatusIcon(contract.contStatus)}
                        <span className="ml-1">{contract.contStatus}</span>
                      </span>
                    )}

                    {/* Expiring Soon Warning */}
                    {contract.contExpiryDate && isExpiringSoon(contract.contExpiryDate) && (
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                        <ExclamationTriangleIcon className="h-3 w-3 mr-1" />
                        Expiring Soon
                      </span>
                    )}
                  </div>

                  {/* Contract Details */}
                  <div className="mt-4 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                    {contract.contValue && (
                      <div className="flex items-center text-gray-600">
                        <CurrencyDollarIcon className="h-4 w-4 mr-2 text-green-600" />
                        <span className="font-medium">
                          {formatCurrency(contract.contValue, contract.contValueCurr)}
                        </span>
                      </div>
                    )}

                    {contract.billingType && (
                      <div className="flex items-center text-gray-600">
                        <ClockIcon className="h-4 w-4 mr-2 text-blue-600" />
                        <span>{contract.billingType}</span>
                      </div>
                    )}

                    {contract.contSignedDate && (
                      <div className="flex items-center text-gray-600">
                        <CalendarIcon className="h-4 w-4 mr-2 text-purple-600" />
                        <span>Signed {formatDate(contract.contSignedDate)}</span>
                      </div>
                    )}

                    {contract.contExpiryDate && (
                      <div className="flex items-center text-gray-600">
                        <CalendarIcon className="h-4 w-4 mr-2 text-red-600" />
                        <span>Expires {formatDate(contract.contExpiryDate)}</span>
                      </div>
                    )}
                  </div>

                  {/* Related Project/Order */}
                  {(contract.project || contract.order) && (
                    <div className="mt-4 flex items-center space-x-4 text-sm text-gray-500">
                      {contract.project && (
                        <div>
                          <span className="font-medium">Project:</span> {contract.project.name}
                        </div>
                      )}
                      {contract.order && (
                        <div>
                          <span className="font-medium">Order:</span> {contract.order.orderTitle}
                        </div>
                      )}
                    </div>
                  )}

                  {/* Next Billing Date */}
                  {contract.nextBillDate && (
                    <div className="mt-3 inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-50 text-blue-700">
                      <CalendarIcon className="h-3 w-3 mr-1" />
                      Next billing: {formatDate(contract.nextBillDate)}
                    </div>
                  )}
                </div>
              </div>

              {/* Contract Dates Timeline */}
              <div className="mt-6 border-t border-gray-200 pt-4">
                <div className="flex items-center justify-between text-xs text-gray-500">
                  <div>
                    Created {formatDate(contract.createdAt)}
                  </div>
                  {contract.updatedAt && (
                    <div>
                      Updated {formatDate(contract.updatedAt)}
                    </div>
                  )}
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      )}
    </div>
  )
}
